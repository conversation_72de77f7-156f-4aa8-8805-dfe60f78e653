{"version": 3, "names": ["_client", "require", "normalizeESLintConfig", "analyzeScope", "baseParse", "client", "LocalClient", "meta", "exports", "name", "version", "parse", "code", "options", "parseForESLint", "normalizedOptions", "ast", "scopeManager", "visitorKeys", "getVisitorKeys"], "sources": ["../src/index.cts"], "sourcesContent": ["import normalizeESLintConfig = require(\"./configuration.cts\");\nimport analyzeScope = require(\"./analyze-scope.cts\");\nimport baseParse = require(\"./parse.cts\");\n\n// @ts-expect-error LocalClient only exists in the cjs build\nimport { LocalClient, WorkerClient } from \"./client.cts\";\nconst client = new (USE_ESM ? WorkerClient : LocalClient)();\n\nexport const meta = {\n  name: PACKAGE_JSON.name,\n  version: PACKAGE_JSON.version,\n};\n\nexport function parse(code: string, options = {}) {\n  return baseParse(code, normalizeESLintConfig(options), client);\n}\n\nexport function parseForESLint(code: string, options = {}) {\n  const normalizedOptions = normalizeESLintConfig(options);\n  const ast = baseParse(code, normalizedOptions, client);\n  const scopeManager = analyzeScope(ast, normalizedOptions, client);\n\n  return { ast, scopeManager, visitorKeys: client.getVisitorKeys() };\n}\n"], "mappings": ";;;;;;;;AAKA,IAAAA,OAAA,GAAAC,OAAA;AAAyD,MALlDC,qBAAqB,GAAAD,OAAA,CAAW,qBAAqB;AAAA,MACrDE,YAAY,GAAAF,OAAA,CAAW,qBAAqB;AAAA,MAC5CG,SAAS,GAAAH,OAAA,CAAW,aAAa;AAIxC,MAAMI,MAAM,GAAG,IAA8BC,mBAAW,CAAE,CAAC;AAEpD,MAAMC,IAAI,GAAAC,OAAA,CAAAD,IAAA,GAAG;EAClBE,IAAI,wBAAmB;EACvBC,OAAO;AACT,CAAC;AAEM,SAASC,KAAKA,CAACC,IAAY,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EAChD,OAAOT,SAAS,CAACQ,IAAI,EAAEV,qBAAqB,CAACW,OAAO,CAAC,EAAER,MAAM,CAAC;AAChE;AAEO,SAASS,cAAcA,CAACF,IAAY,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EACzD,MAAME,iBAAiB,GAAGb,qBAAqB,CAACW,OAAO,CAAC;EACxD,MAAMG,GAAG,GAAGZ,SAAS,CAACQ,IAAI,EAAEG,iBAAiB,EAAEV,MAAM,CAAC;EACtD,MAAMY,YAAY,GAAGd,YAAY,CAACa,GAAG,EAAED,iBAAiB,EAAEV,MAAM,CAAC;EAEjE,OAAO;IAAEW,GAAG;IAAEC,YAAY;IAAEC,WAAW,EAAEb,MAAM,CAACc,cAAc,CAAC;EAAE,CAAC;AACpE", "ignoreList": []}