{"version": 3, "names": ["semver", "require", "convert", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "v", "w", "split", "process", "versions", "node", "resolve", "r", "paths", "b", "M", "f", "_findPath", "_nodeModulePaths", "concat", "Error", "code", "isRunningMinSupportedCoreVersion", "module", "exports", "parse", "options", "client", "minSupportedCoreVersion", "satisfies", "getVersion", "ast", "parserOptions", "<PERSON><PERSON><PERSON><PERSON>", "convertFile", "getTokLabels", "getVisitorKeys", "err", "convertError"], "sources": ["../src/parse.cts"], "sourcesContent": ["\"use strict\";\n\nimport semver = require(\"semver\");\nimport convert = require(\"./convert/index.cts\");\nimport type { Options } from \"./types.cts\";\nimport type { Client } from \"./client.cts\";\n\nconst babelParser = require(\n  require.resolve(\"@babel/parser\", {\n    paths: [require.resolve(\"@babel/core/package.json\")],\n  }),\n);\n\nlet isRunningMinSupportedCoreVersion: boolean = null;\n\nexport = function parse(code: string, options: Options, client: Client) {\n  // Ensure we're using a version of `@babel/core` that includes `parse()` and `tokTypes`.\n  const minSupportedCoreVersion = REQUIRED_VERSION(\">=7.2.0\");\n\n  if (typeof isRunningMinSupportedCoreVersion !== \"boolean\") {\n    isRunningMinSupportedCoreVersion = semver.satisfies(\n      client.getVersion(),\n      minSupportedCoreVersion,\n    );\n  }\n\n  if (!isRunningMinSupportedCoreVersion) {\n    throw new Error(\n      `@babel/eslint-parser@${\n        PACKAGE_JSON.version\n      } does not support @babel/core@${client.getVersion()}. Please upgrade to @babel/core@${minSupportedCoreVersion}.`,\n    );\n  }\n\n  const { ast, parserOptions } = client.maybeParse(code, options);\n\n  if (ast) return ast;\n\n  try {\n    return convert.convertFile(\n      babelParser.parse(code, parserOptions),\n      code,\n      client.getTokLabels(),\n      client.getVisitorKeys(),\n    );\n  } catch (err) {\n    throw convert.convertError(err);\n  }\n};\n"], "mappings": "AAAA,YAAY;;AAAC,MAENA,MAAM,GAAAC,OAAA,CAAW,QAAQ;AAAA,MACzBC,OAAO,GAAAD,OAAA,CAAW,qBAAqB;AAI9C,MAAME,WAAW,GAAGF,OAAO,CACzB,GAAAG,CAAA,EAAAC,CAAA,MAAAD,CAAA,GAAAA,CAAA,CAAAE,KAAA,OAAAD,CAAA,GAAAA,CAAA,CAAAC,KAAA,QAAAF,CAAA,OAAAC,CAAA,OAAAD,CAAA,OAAAC,CAAA,QAAAD,CAAA,QAAAC,CAAA,MAAAE,OAAA,CAAAC,QAAA,CAAAC,IAAA,WAAAR,OAAA,CAAAS,OAAA,IAAAC,CAAA;EAAAC,KAAA,GAAAC,CAAA;AAAA,GAAAC,CAAA,GAAAb,OAAA;EAAA,IAAAc,CAAA,GAAAD,CAAA,CAAAE,SAAA,CAAAL,CAAA,EAAAG,CAAA,CAAAG,gBAAA,CAAAJ,CAAA,EAAAK,MAAA,CAAAL,CAAA;EAAA,IAAAE,CAAA,SAAAA,CAAA;EAAAA,CAAA,OAAAI,KAAA,2BAAAR,CAAA;EAAAI,CAAA,CAAAK,IAAA;EAAA,MAAAL,CAAA;AAAA,GAAgB,eAAe,EAAE;EAC/BH,KAAK,EAAE,CAACX,OAAO,CAACS,OAAO,CAAC,0BAA0B,CAAC;AACrD,CAAC,CACH,CAAC;AAED,IAAIW,gCAAyC,GAAG,IAAI;AAACC,MAAA,CAAAC,OAAA,GAE5C,SAASC,KAAKA,CAACJ,IAAY,EAAEK,OAAgB,EAAEC,MAAc,EAAE;EAEtE,MAAMC,uBAAuB,GAAoB,SAAU;EAE3D,IAAI,OAAON,gCAAgC,KAAK,SAAS,EAAE;IACzDA,gCAAgC,GAAGrB,MAAM,CAAC4B,SAAS,CACjDF,MAAM,CAACG,UAAU,CAAC,CAAC,EACnBF,uBACF,CAAC;EACH;EAEA,IAAI,CAACN,gCAAgC,EAAE;IACrC,MAAM,IAAIF,KAAK,CACb,iEAEiCO,MAAM,CAACG,UAAU,CAAC,CAAC,mCAAmCF,uBAAuB,GAChH,CAAC;EACH;EAEA,MAAM;IAAEG,GAAG;IAAEC;EAAc,CAAC,GAAGL,MAAM,CAACM,UAAU,CAACZ,IAAI,EAAEK,OAAO,CAAC;EAE/D,IAAIK,GAAG,EAAE,OAAOA,GAAG;EAEnB,IAAI;IACF,OAAO5B,OAAO,CAAC+B,WAAW,CACxB9B,WAAW,CAACqB,KAAK,CAACJ,IAAI,EAAEW,aAAa,CAAC,EACtCX,IAAI,EACJM,MAAM,CAACQ,YAAY,CAAC,CAAC,EACrBR,MAAM,CAACS,cAAc,CAAC,CACxB,CAAC;EACH,CAAC,CAAC,OAAOC,GAAG,EAAE;IACZ,MAAMlC,OAAO,CAACmC,YAAY,CAACD,GAAG,CAAC;EACjC;AACF,CAAC", "ignoreList": []}